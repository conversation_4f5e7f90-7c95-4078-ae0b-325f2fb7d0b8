import React from 'react';
import { <PERSON><PERSON>, Card, Space, Typography, Tag } from 'antd';
import { CrownOutlined, UserOutlined, SettingOutlined } from '@ant-design/icons';
import styled from 'styled-components';
import { useAuthStore } from '../../stores/authStore';
import { theme } from '../../styles/theme';

const { Title, Text } = Typography;

const TestPanelContainer = styled.div`
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  
  .test-panel {
    background: white;
    border: 2px solid ${theme.colors.primary[200]};
    border-radius: ${theme.borderRadius.lg};
    box-shadow: ${theme.boxShadow.xl};
    padding: ${theme.spacing[4]};
    min-width: 280px;
    
    .panel-title {
      font-size: ${theme.typography.fontSize.base};
      font-weight: ${theme.typography.fontWeight.bold};
      color: ${theme.colors.primary[600]};
      margin-bottom: ${theme.spacing[3]};
      display: flex;
      align-items: center;
      gap: ${theme.spacing[2]};
    }
    
    .current-user {
      background: ${theme.colors.gray[50]};
      padding: ${theme.spacing[2]};
      border-radius: ${theme.borderRadius.md};
      margin-bottom: ${theme.spacing[3]};
      
      .user-info {
        display: flex;
        align-items: center;
        gap: ${theme.spacing[2]};
        
        .user-role {
          &.super_admin {
            background: ${theme.colors.warning[500]};
            color: white;
          }
          
          &.admin {
            background: ${theme.colors.primary[500]};
            color: white;
          }
          
          &.user {
            background: ${theme.colors.gray[500]};
            color: white;
          }
        }
      }
    }
    
    .test-buttons {
      display: flex;
      flex-direction: column;
      gap: ${theme.spacing[2]};
      
      .test-btn {
        width: 100%;
        text-align: left;
        
        &.super-admin {
          border-color: ${theme.colors.warning[300]};
          color: ${theme.colors.warning[600]};
          
          &:hover {
            border-color: ${theme.colors.warning[500]};
            color: ${theme.colors.warning[700]};
          }
        }
        
        &.admin {
          border-color: ${theme.colors.primary[300]};
          color: ${theme.colors.primary[600]};
          
          &:hover {
            border-color: ${theme.colors.primary[500]};
            color: ${theme.colors.primary[700]};
          }
        }
      }
    }
  }
`;

const AdminTestPanel: React.FC = () => {
  const { user, setUser } = useAuthStore();

  // 仅在开发环境显示
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  const setTestUser = (role: 'user' | 'admin' | 'super_admin') => {
    const testUsers = {
      user: {
        id: '1',
        username: 'testuser',
        email: '<EMAIL>',
        phone: '13800000001',
        role: 'user' as const,
        avatar: '/api/placeholder/40/40',
        register_type: 'phone' as const,
        phone_verified: true,
        email_verified: false,
        status: 'active' as const,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      admin: {
        id: '2',
        username: 'testadmin',
        email: '<EMAIL>',
        phone: '13800000002',
        role: 'admin' as const,
        avatar: '/api/placeholder/40/40',
        register_type: 'phone' as const,
        phone_verified: true,
        email_verified: true,
        status: 'active' as const,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      super_admin: {
        id: '3',
        username: 'superadmin',
        email: '<EMAIL>',
        phone: '13800000003',
        role: 'super_admin' as const,
        avatar: '/api/placeholder/40/40',
        register_type: 'phone' as const,
        phone_verified: true,
        email_verified: true,
        status: 'active' as const,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    };

    setUser(testUsers[role]);
  };

  const getRoleText = (role?: string) => {
    switch (role) {
      case 'super_admin':
        return '超级管理员';
      case 'admin':
        return '管理员';
      case 'user':
        return '普通用户';
      default:
        return '未登录';
    }
  };

  return (
    <TestPanelContainer>
      <div className="test-panel">
        <div className="panel-title">
          <SettingOutlined />
          测试面板 (开发模式)
        </div>
        
        <div className="current-user">
          <Text type="secondary" style={{ fontSize: '12px' }}>当前用户:</Text>
          <div className="user-info">
            <span>{user?.username || '未登录'}</span>
            {user?.role && (
              <Tag className={`user-role ${user.role}`} size="small">
                {getRoleText(user.role)}
              </Tag>
            )}
          </div>
        </div>
        
        <div className="test-buttons">
          <Button
            className="test-btn"
            icon={<UserOutlined />}
            onClick={() => setTestUser('user')}
          >
            切换为普通用户
          </Button>
          
          <Button
            className="test-btn admin"
            icon={<SettingOutlined />}
            onClick={() => setTestUser('admin')}
          >
            切换为管理员
          </Button>
          
          <Button
            className="test-btn super-admin"
            icon={<CrownOutlined />}
            onClick={() => setTestUser('super_admin')}
          >
            切换为超级管理员
          </Button>
          
          <Button
            size="small"
            type="text"
            onClick={() => setUser(null)}
          >
            退出登录
          </Button>
        </div>
      </div>
    </TestPanelContainer>
  );
};

export default AdminTestPanel;
