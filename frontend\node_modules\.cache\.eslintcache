[{"D:\\claude镜像\\收书卖书\\frontend\\src\\index.tsx": "1", "D:\\claude镜像\\收书卖书\\frontend\\src\\reportWebVitals.ts": "2", "D:\\claude镜像\\收书卖书\\frontend\\src\\App.tsx": "3", "D:\\claude镜像\\收书卖书\\frontend\\src\\stores\\authStore.ts": "4", "D:\\claude镜像\\收书卖书\\frontend\\src\\components\\layout\\Layout.tsx": "5", "D:\\claude镜像\\收书卖书\\frontend\\src\\components\\ui\\ProtectedRoute.tsx": "6", "D:\\claude镜像\\收书卖书\\frontend\\src\\components\\ui\\ErrorBoundary.tsx": "7", "D:\\claude镜像\\收书卖书\\frontend\\src\\components\\business\\UserProfile.tsx": "8", "D:\\claude镜像\\收书卖书\\frontend\\src\\pages\\Admin\\Dashboard.tsx": "9", "D:\\claude镜像\\收书卖书\\frontend\\src\\pages\\Auth\\AuthPage.tsx": "10", "D:\\claude镜像\\收书卖书\\frontend\\src\\pages\\Checkout\\CheckoutPage.tsx": "11", "D:\\claude镜像\\收书卖书\\frontend\\src\\pages\\Orders\\OrdersPage.tsx": "12", "D:\\claude镜像\\收书卖书\\frontend\\src\\pages\\Books\\BookDetailPage.tsx": "13", "D:\\claude镜像\\收书卖书\\frontend\\src\\pages\\Books\\BooksListPage.tsx": "14", "D:\\claude镜像\\收书卖书\\frontend\\src\\pages\\Cart\\CartPage.tsx": "15", "D:\\claude镜像\\收书卖书\\frontend\\src\\pages\\Home\\HomePage.tsx": "16", "D:\\claude镜像\\收书卖书\\frontend\\src\\components\\layout\\Header.tsx": "17", "D:\\claude镜像\\收书卖书\\frontend\\src\\components\\layout\\Footer.tsx": "18", "D:\\claude镜像\\收书卖书\\frontend\\src\\components\\ui\\Button.tsx": "19", "D:\\claude镜像\\收书卖书\\frontend\\src\\components\\ui\\Card.tsx": "20", "D:\\claude镜像\\收书卖书\\frontend\\src\\pages\\Admin\\AdminLayout.tsx": "21", "D:\\claude镜像\\收书卖书\\frontend\\src\\pages\\Admin\\AdminOverview.tsx": "22", "D:\\claude镜像\\收书卖书\\frontend\\src\\pages\\Admin\\AdminBooks.tsx": "23", "D:\\claude镜像\\收书卖书\\frontend\\src\\pages\\Admin\\AdminOrders.tsx": "24", "D:\\claude镜像\\收书卖书\\frontend\\src\\pages\\Admin\\AdminCategories.tsx": "25", "D:\\claude镜像\\收书卖书\\frontend\\src\\pages\\Admin\\AdminUsers.tsx": "26", "D:\\claude镜像\\收书卖书\\frontend\\src\\stores\\cartStore.ts": "27", "D:\\claude镜像\\收书卖书\\frontend\\src\\services\\auth.ts": "28", "D:\\claude镜像\\收书卖书\\frontend\\src\\services\\orders.ts": "29", "D:\\claude镜像\\收书卖书\\frontend\\src\\services\\users.ts": "30", "D:\\claude镜像\\收书卖书\\frontend\\src\\styles\\theme.ts": "31", "D:\\claude镜像\\收书卖书\\frontend\\src\\components\\business\\PaymentSystem.tsx": "32", "D:\\claude镜像\\收书卖书\\frontend\\src\\components\\ui\\Loading.tsx": "33", "D:\\claude镜像\\收书卖书\\frontend\\src\\services\\books.ts": "34", "D:\\claude镜像\\收书卖书\\frontend\\src\\components\\business\\ReviewSystem.tsx": "35", "D:\\claude镜像\\收书卖书\\frontend\\src\\services\\api.ts": "36", "D:\\claude镜像\\收书卖书\\frontend\\src\\services\\favorites.ts": "37", "D:\\claude镜像\\收书卖书\\frontend\\src\\components\\ui\\ImageGallery.tsx": "38", "D:\\claude镜像\\收书卖书\\frontend\\src\\components\\business\\EnhancedBookCard.tsx": "39", "D:\\claude镜像\\收书卖书\\frontend\\src\\components\\business\\EnhancedFilters.tsx": "40", "D:\\claude镜像\\收书卖书\\frontend\\src\\services\\categories.ts": "41", "D:\\claude镜像\\收书卖书\\frontend\\src\\components\\business\\EnhancedCart.tsx": "42", "D:\\claude镜像\\收书卖书\\frontend\\src\\components\\ui\\EmptyState.tsx": "43", "D:\\claude镜像\\收书卖书\\frontend\\src\\services\\upload.ts": "44", "D:\\claude镜像\\收书卖书\\frontend\\src\\services\\admin.ts": "45", "D:\\claude镜像\\收书卖书\\frontend\\src\\services\\reviews.ts": "46", "D:\\claude镜像\\收书卖书\\frontend\\src\\components\\business\\FavoriteButton.tsx": "47", "D:\\claude镜像\\收书卖书\\frontend\\src\\components\\ui\\LazyImage.tsx": "48", "D:\\claude镜像\\收书卖书\\frontend\\src\\utils\\errorHandler.ts": "49", "D:\\claude镜像\\收书卖书\\frontend\\src\\pages\\Admin\\SuperAdminDashboard.tsx": "50", "D:\\claude镜像\\收书卖书\\frontend\\src\\pages\\Admin\\SuperAdminLayout.tsx": "51", "D:\\claude镜像\\收书卖书\\frontend\\src\\pages\\Admin\\AdminManagement.tsx": "52", "D:\\claude镜像\\收书卖书\\frontend\\src\\components\\ui\\index.ts": "53", "D:\\claude镜像\\收书卖书\\frontend\\src\\components\\ui\\PageTransition.tsx": "54", "D:\\claude镜像\\收书卖书\\frontend\\src\\components\\ui\\StatCard.tsx": "55", "D:\\claude镜像\\收书卖书\\frontend\\src\\components\\ui\\FormField.tsx": "56", "D:\\claude镜像\\收书卖书\\frontend\\src\\components\\ui\\AnimatedWrapper.tsx": "57", "D:\\claude镜像\\收书卖书\\frontend\\src\\components\\ui\\Modal.tsx": "58", "D:\\claude镜像\\收书卖书\\frontend\\src\\components\\ui\\NetworkError.tsx": "59", "D:\\claude镜像\\收书卖书\\frontend\\src\\utils\\animations.ts": "60", "D:\\claude镜像\\收书卖书\\frontend\\src\\pages\\Admin\\AdminPortal.tsx": "61", "D:\\claude镜像\\收书卖书\\frontend\\src\\components\\debug\\AdminTestPanel.tsx": "62"}, {"size": 554, "mtime": 1753788344330, "results": "63", "hashOfConfig": "64"}, {"size": 425, "mtime": 1753788344016, "results": "65", "hashOfConfig": "64"}, {"size": 5491, "mtime": 1753870080135, "results": "66", "hashOfConfig": "64"}, {"size": 4946, "mtime": 1753870349424, "results": "67", "hashOfConfig": "64"}, {"size": 695, "mtime": 1753804216462, "results": "68", "hashOfConfig": "64"}, {"size": 1516, "mtime": 1753804206283, "results": "69", "hashOfConfig": "64"}, {"size": 2985, "mtime": 1753867065226, "results": "70", "hashOfConfig": "64"}, {"size": 23591, "mtime": 1753814839834, "results": "71", "hashOfConfig": "64"}, {"size": 955, "mtime": 1753805012221, "results": "72", "hashOfConfig": "64"}, {"size": 18500, "mtime": 1753812618634, "results": "73", "hashOfConfig": "64"}, {"size": 22201, "mtime": 1753814617014, "results": "74", "hashOfConfig": "64"}, {"size": 24685, "mtime": 1753812665294, "results": "75", "hashOfConfig": "64"}, {"size": 33487, "mtime": 1753865787692, "results": "76", "hashOfConfig": "64"}, {"size": 26820, "mtime": 1753868057732, "results": "77", "hashOfConfig": "64"}, {"size": 2690, "mtime": 1753814518280, "results": "78", "hashOfConfig": "64"}, {"size": 25992, "mtime": 1753815264075, "results": "79", "hashOfConfig": "64"}, {"size": 4930, "mtime": 1753869995772, "results": "80", "hashOfConfig": "64"}, {"size": 3015, "mtime": 1753804259951, "results": "81", "hashOfConfig": "64"}, {"size": 7115, "mtime": 1753813490188, "results": "82", "hashOfConfig": "64"}, {"size": 5574, "mtime": 1753813522983, "results": "83", "hashOfConfig": "64"}, {"size": 4177, "mtime": 1753805039714, "results": "84", "hashOfConfig": "64"}, {"size": 9419, "mtime": 1753805084948, "results": "85", "hashOfConfig": "64"}, {"size": 15456, "mtime": 1753805419715, "results": "86", "hashOfConfig": "64"}, {"size": 12790, "mtime": 1753805480314, "results": "87", "hashOfConfig": "64"}, {"size": 10043, "mtime": 1753810474077, "results": "88", "hashOfConfig": "64"}, {"size": 7861, "mtime": 1753805120950, "results": "89", "hashOfConfig": "64"}, {"size": 4015, "mtime": 1753870944319, "results": "90", "hashOfConfig": "64"}, {"size": 2102, "mtime": 1753803247890, "results": "91", "hashOfConfig": "64"}, {"size": 1613, "mtime": 1753803272335, "results": "92", "hashOfConfig": "64"}, {"size": 1201, "mtime": 1753805177353, "results": "93", "hashOfConfig": "64"}, {"size": 7056, "mtime": 1753868855806, "results": "94", "hashOfConfig": "64"}, {"size": 16682, "mtime": 1753811785625, "results": "95", "hashOfConfig": "64"}, {"size": 8083, "mtime": 1753813567048, "results": "96", "hashOfConfig": "64"}, {"size": 2728, "mtime": 1753803262017, "results": "97", "hashOfConfig": "64"}, {"size": 17249, "mtime": 1753870667684, "results": "98", "hashOfConfig": "64"}, {"size": 1551, "mtime": 1753806203612, "results": "99", "hashOfConfig": "64"}, {"size": 1746, "mtime": 1753807130038, "results": "100", "hashOfConfig": "64"}, {"size": 9427, "mtime": 1753865667587, "results": "101", "hashOfConfig": "64"}, {"size": 13415, "mtime": 1753815407063, "results": "102", "hashOfConfig": "64"}, {"size": 14735, "mtime": 1753815296506, "results": "103", "hashOfConfig": "64"}, {"size": 1460, "mtime": 1753803281466, "results": "104", "hashOfConfig": "64"}, {"size": 20111, "mtime": 1753871167765, "results": "105", "hashOfConfig": "64"}, {"size": 6307, "mtime": 1753815694260, "results": "106", "hashOfConfig": "64"}, {"size": 2004, "mtime": 1753803294032, "results": "107", "hashOfConfig": "64"}, {"size": 1866, "mtime": 1753805205458, "results": "108", "hashOfConfig": "64"}, {"size": 3703, "mtime": 1753808790201, "results": "109", "hashOfConfig": "64"}, {"size": 3332, "mtime": 1753811553757, "results": "110", "hashOfConfig": "64"}, {"size": 4535, "mtime": 1753806372036, "results": "111", "hashOfConfig": "64"}, {"size": 8446, "mtime": 1753806125366, "results": "112", "hashOfConfig": "64"}, {"size": 13931, "mtime": 1753869694932, "results": "113", "hashOfConfig": "64"}, {"size": 11789, "mtime": 1753869631217, "results": "114", "hashOfConfig": "64"}, {"size": 13129, "mtime": 1753869772678, "results": "115", "hashOfConfig": "64"}, {"size": 1269, "mtime": 1753867192084, "results": "116", "hashOfConfig": "64"}, {"size": 4545, "mtime": 1753866907532, "results": "117", "hashOfConfig": "64"}, {"size": 8771, "mtime": 1753866377451, "results": "118", "hashOfConfig": "64"}, {"size": 8009, "mtime": 1753866492318, "results": "119", "hashOfConfig": "64"}, {"size": 4465, "mtime": 1753866873316, "results": "120", "hashOfConfig": "64"}, {"size": 8463, "mtime": 1753866536507, "results": "121", "hashOfConfig": "64"}, {"size": 5446, "mtime": 1753867140125, "results": "122", "hashOfConfig": "64"}, {"size": 8560, "mtime": 1753866788069, "results": "123", "hashOfConfig": "64"}, {"size": 10488, "mtime": 1753869914589, "results": "124", "hashOfConfig": "64"}, {"size": 6007, "mtime": 1753871377745, "results": "125", "hashOfConfig": "64"}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "18twikc", {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 19, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\claude镜像\\收书卖书\\frontend\\src\\index.tsx", [], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\reportWebVitals.ts", [], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\App.tsx", [], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\stores\\authStore.ts", [], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\components\\layout\\Layout.tsx", [], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\components\\ui\\ProtectedRoute.tsx", [], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\components\\ui\\ErrorBoundary.tsx", ["312", "313", "314", "315", "316"], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\components\\business\\UserProfile.tsx", ["317", "318", "319", "320", "321", "322", "323", "324", "325", "326"], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\pages\\Admin\\Dashboard.tsx", [], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\pages\\Auth\\AuthPage.tsx", ["327", "328", "329", "330"], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\pages\\Checkout\\CheckoutPage.tsx", ["331", "332", "333", "334", "335", "336", "337", "338", "339", "340", "341", "342", "343"], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\pages\\Orders\\OrdersPage.tsx", ["344", "345", "346", "347", "348", "349"], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\pages\\Books\\BookDetailPage.tsx", ["350", "351", "352", "353", "354", "355", "356", "357", "358", "359", "360", "361", "362", "363", "364", "365", "366", "367", "368"], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\pages\\Books\\BooksListPage.tsx", ["369", "370", "371", "372", "373", "374", "375", "376", "377", "378", "379", "380", "381", "382", "383", "384", "385", "386"], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\pages\\Cart\\CartPage.tsx", [], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\pages\\Home\\HomePage.tsx", ["387", "388", "389", "390", "391", "392", "393", "394", "395", "396", "397", "398", "399", "400", "401", "402"], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\components\\layout\\Header.tsx", ["403"], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\components\\layout\\Footer.tsx", [], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\components\\ui\\Button.tsx", [], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\components\\ui\\Card.tsx", [], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\pages\\Admin\\AdminLayout.tsx", ["404", "405"], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\pages\\Admin\\AdminOverview.tsx", ["406", "407", "408", "409", "410", "411"], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\pages\\Admin\\AdminBooks.tsx", ["412", "413"], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\pages\\Admin\\AdminOrders.tsx", ["414", "415", "416"], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\pages\\Admin\\AdminCategories.tsx", ["417", "418"], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\pages\\Admin\\AdminUsers.tsx", ["419", "420"], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\stores\\cartStore.ts", [], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\services\\auth.ts", [], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\services\\orders.ts", [], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\services\\users.ts", [], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\styles\\theme.ts", [], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\components\\business\\PaymentSystem.tsx", ["421", "422", "423", "424", "425"], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\components\\ui\\Loading.tsx", [], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\services\\books.ts", [], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\components\\business\\ReviewSystem.tsx", ["426", "427", "428", "429", "430", "431", "432"], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\services\\api.ts", [], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\services\\favorites.ts", [], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\components\\ui\\ImageGallery.tsx", [], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\components\\business\\EnhancedBookCard.tsx", ["433", "434", "435", "436", "437", "438", "439"], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\components\\business\\EnhancedFilters.tsx", ["440", "441", "442", "443", "444", "445", "446", "447", "448", "449"], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\services\\categories.ts", [], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\components\\business\\EnhancedCart.tsx", ["450", "451", "452", "453", "454", "455", "456"], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\components\\ui\\EmptyState.tsx", ["457", "458"], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\services\\upload.ts", [], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\services\\admin.ts", [], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\services\\reviews.ts", [], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\components\\business\\FavoriteButton.tsx", ["459"], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\components\\ui\\LazyImage.tsx", ["460"], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\utils\\errorHandler.ts", [], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\pages\\Admin\\SuperAdminDashboard.tsx", ["461", "462", "463", "464", "465", "466"], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\pages\\Admin\\SuperAdminLayout.tsx", ["467", "468", "469", "470", "471", "472", "473", "474", "475"], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\pages\\Admin\\AdminManagement.tsx", ["476", "477", "478"], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\components\\ui\\index.ts", [], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\components\\ui\\PageTransition.tsx", ["479", "480"], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\components\\ui\\StatCard.tsx", [], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\components\\ui\\FormField.tsx", [], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\components\\ui\\AnimatedWrapper.tsx", ["481"], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\components\\ui\\Modal.tsx", [], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\components\\ui\\NetworkError.tsx", [], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\utils\\animations.ts", ["482"], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\pages\\Admin\\AdminPortal.tsx", ["483", "484"], [], "D:\\claude镜像\\收书卖书\\frontend\\src\\components\\debug\\AdminTestPanel.tsx", ["485", "486", "487"], [], {"ruleId": "488", "severity": 1, "message": "489", "line": 5, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 5, "endColumn": 17}, {"ruleId": "488", "severity": 1, "message": "492", "line": 6, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 6, "endColumn": 15}, {"ruleId": "488", "severity": 1, "message": "493", "line": 7, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 7, "endColumn": 28}, {"ruleId": "488", "severity": 1, "message": "494", "line": 9, "column": 8, "nodeType": "490", "messageId": "491", "endLine": 9, "endColumn": 14}, {"ruleId": "488", "severity": 1, "message": "495", "line": 11, "column": 10, "nodeType": "490", "messageId": "491", "endLine": 11, "endColumn": 15}, {"ruleId": "488", "severity": 1, "message": "496", "line": 15, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 15, "endColumn": 12}, {"ruleId": "488", "severity": 1, "message": "497", "line": 17, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 17, "endColumn": 8}, {"ruleId": "488", "severity": 1, "message": "498", "line": 18, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 18, "endColumn": 8}, {"ruleId": "488", "severity": 1, "message": "499", "line": 30, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 30, "endColumn": 13}, {"ruleId": "488", "severity": 1, "message": "500", "line": 31, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 31, "endColumn": 14}, {"ruleId": "488", "severity": 1, "message": "501", "line": 33, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 33, "endColumn": 17}, {"ruleId": "488", "severity": 1, "message": "502", "line": 34, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 34, "endColumn": 22}, {"ruleId": "488", "severity": 1, "message": "503", "line": 35, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 35, "endColumn": 16}, {"ruleId": "488", "severity": 1, "message": "504", "line": 45, "column": 16, "nodeType": "490", "messageId": "491", "endLine": 45, "endColumn": 20}, {"ruleId": "488", "severity": 1, "message": "505", "line": 45, "column": 22, "nodeType": "490", "messageId": "491", "endLine": 45, "endColumn": 31}, {"ruleId": "488", "severity": 1, "message": "506", "line": 9, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 9, "endColumn": 8}, {"ruleId": "488", "severity": 1, "message": "507", "line": 10, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 10, "endColumn": 10}, {"ruleId": "488", "severity": 1, "message": "508", "line": 13, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 13, "endColumn": 6}, {"ruleId": "488", "severity": 1, "message": "509", "line": 14, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 14, "endColumn": 6}, {"ruleId": "488", "severity": 1, "message": "508", "line": 3, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 3, "endColumn": 6}, {"ruleId": "488", "severity": 1, "message": "509", "line": 4, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 4, "endColumn": 6}, {"ruleId": "488", "severity": 1, "message": "510", "line": 5, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 5, "endColumn": 7}, {"ruleId": "488", "severity": 1, "message": "511", "line": 6, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 6, "endColumn": 8}, {"ruleId": "488", "severity": 1, "message": "506", "line": 8, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 8, "endColumn": 8}, {"ruleId": "488", "severity": 1, "message": "512", "line": 11, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 11, "endColumn": 8}, {"ruleId": "488", "severity": 1, "message": "513", "line": 12, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 12, "endColumn": 7}, {"ruleId": "488", "severity": 1, "message": "514", "line": 13, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 13, "endColumn": 8}, {"ruleId": "488", "severity": 1, "message": "515", "line": 24, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 24, "endColumn": 21}, {"ruleId": "488", "severity": 1, "message": "516", "line": 25, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 25, "endColumn": 22}, {"ruleId": "488", "severity": 1, "message": "504", "line": 40, "column": 16, "nodeType": "490", "messageId": "491", "endLine": 40, "endColumn": 20}, {"ruleId": "488", "severity": 1, "message": "517", "line": 41, "column": 9, "nodeType": "490", "messageId": "491", "endLine": 41, "endColumn": 15}, {"ruleId": "488", "severity": 1, "message": "518", "line": 392, "column": 11, "nodeType": "490", "messageId": "491", "endLine": 392, "endColumn": 15}, {"ruleId": "488", "severity": 1, "message": "506", "line": 7, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 7, "endColumn": 8}, {"ruleId": "488", "severity": 1, "message": "519", "line": 15, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 15, "endColumn": 8}, {"ruleId": "488", "severity": 1, "message": "520", "line": 26, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 26, "endColumn": 18}, {"ruleId": "488", "severity": 1, "message": "505", "line": 37, "column": 22, "nodeType": "490", "messageId": "491", "endLine": 37, "endColumn": 31}, {"ruleId": "488", "severity": 1, "message": "521", "line": 40, "column": 9, "nodeType": "490", "messageId": "491", "endLine": 40, "endColumn": 13}, {"ruleId": "522", "severity": 1, "message": "523", "line": 398, "column": 6, "nodeType": "524", "endLine": 398, "endColumn": 17, "suggestions": "525"}, {"ruleId": "488", "severity": 1, "message": "507", "line": 9, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 9, "endColumn": 10}, {"ruleId": "488", "severity": 1, "message": "514", "line": 10, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 10, "endColumn": 8}, {"ruleId": "488", "severity": 1, "message": "513", "line": 14, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 14, "endColumn": 7}, {"ruleId": "488", "severity": 1, "message": "526", "line": 16, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 16, "endColumn": 7}, {"ruleId": "488", "severity": 1, "message": "498", "line": 18, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 18, "endColumn": 8}, {"ruleId": "488", "severity": 1, "message": "496", "line": 21, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 21, "endColumn": 12}, {"ruleId": "488", "severity": 1, "message": "500", "line": 27, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 27, "endColumn": 14}, {"ruleId": "488", "severity": 1, "message": "527", "line": 29, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 29, "endColumn": 19}, {"ruleId": "488", "severity": 1, "message": "499", "line": 32, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 32, "endColumn": 13}, {"ruleId": "488", "severity": 1, "message": "528", "line": 34, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 34, "endColumn": 15}, {"ruleId": "488", "severity": 1, "message": "529", "line": 35, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 35, "endColumn": 18}, {"ruleId": "488", "severity": 1, "message": "530", "line": 36, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 36, "endColumn": 15}, {"ruleId": "488", "severity": 1, "message": "501", "line": 37, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 37, "endColumn": 17}, {"ruleId": "488", "severity": 1, "message": "531", "line": 38, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 38, "endColumn": 16}, {"ruleId": "488", "severity": 1, "message": "504", "line": 55, "column": 16, "nodeType": "490", "messageId": "491", "endLine": 55, "endColumn": 20}, {"ruleId": "488", "severity": 1, "message": "518", "line": 620, "column": 28, "nodeType": "490", "messageId": "491", "endLine": 620, "endColumn": 32}, {"ruleId": "488", "severity": 1, "message": "532", "line": 626, "column": 10, "nodeType": "490", "messageId": "491", "endLine": 626, "endColumn": 26}, {"ruleId": "488", "severity": 1, "message": "533", "line": 626, "column": 28, "nodeType": "490", "messageId": "491", "endLine": 626, "endColumn": 47}, {"ruleId": "522", "severity": 1, "message": "534", "line": 634, "column": 6, "nodeType": "524", "endLine": 634, "endColumn": 10, "suggestions": "535"}, {"ruleId": "488", "severity": 1, "message": "508", "line": 3, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 3, "endColumn": 6}, {"ruleId": "488", "severity": 1, "message": "509", "line": 4, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 4, "endColumn": 6}, {"ruleId": "488", "severity": 1, "message": "506", "line": 6, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 6, "endColumn": 8}, {"ruleId": "488", "severity": 1, "message": "526", "line": 8, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 8, "endColumn": 7}, {"ruleId": "488", "severity": 1, "message": "519", "line": 9, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 9, "endColumn": 8}, {"ruleId": "488", "severity": 1, "message": "536", "line": 11, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 11, "endColumn": 6}, {"ruleId": "488", "severity": 1, "message": "537", "line": 12, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 12, "endColumn": 9}, {"ruleId": "488", "severity": 1, "message": "538", "line": 13, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 13, "endColumn": 11}, {"ruleId": "488", "severity": 1, "message": "539", "line": 20, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 20, "endColumn": 17}, {"ruleId": "488", "severity": 1, "message": "540", "line": 24, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 24, "endColumn": 16}, {"ruleId": "488", "severity": 1, "message": "541", "line": 31, "column": 8, "nodeType": "490", "messageId": "491", "endLine": 31, "endColumn": 22}, {"ruleId": "488", "severity": 1, "message": "542", "line": 33, "column": 8, "nodeType": "490", "messageId": "491", "endLine": 33, "endColumn": 14}, {"ruleId": "488", "severity": 1, "message": "504", "line": 41, "column": 16, "nodeType": "490", "messageId": "491", "endLine": 41, "endColumn": 20}, {"ruleId": "488", "severity": 1, "message": "543", "line": 43, "column": 9, "nodeType": "490", "messageId": "491", "endLine": 43, "endColumn": 14}, {"ruleId": "488", "severity": 1, "message": "544", "line": 551, "column": 24, "nodeType": "490", "messageId": "491", "endLine": 551, "endColumn": 39}, {"ruleId": "522", "severity": 1, "message": "545", "line": 590, "column": 6, "nodeType": "524", "endLine": 590, "endColumn": 35, "suggestions": "546"}, {"ruleId": "488", "severity": 1, "message": "547", "line": 676, "column": 9, "nodeType": "490", "messageId": "491", "endLine": 676, "endColumn": 21}, {"ruleId": "488", "severity": 1, "message": "548", "line": 731, "column": 9, "nodeType": "490", "messageId": "491", "endLine": 731, "endColumn": 25}, {"ruleId": "488", "severity": 1, "message": "508", "line": 3, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 3, "endColumn": 6}, {"ruleId": "488", "severity": 1, "message": "509", "line": 4, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 4, "endColumn": 6}, {"ruleId": "488", "severity": 1, "message": "536", "line": 8, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 8, "endColumn": 6}, {"ruleId": "488", "severity": 1, "message": "496", "line": 9, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 9, "endColumn": 12}, {"ruleId": "488", "severity": 1, "message": "513", "line": 10, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 10, "endColumn": 7}, {"ruleId": "488", "severity": 1, "message": "549", "line": 11, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 11, "endColumn": 9}, {"ruleId": "488", "severity": 1, "message": "550", "line": 12, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 12, "endColumn": 7}, {"ruleId": "488", "severity": 1, "message": "507", "line": 13, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 13, "endColumn": 10}, {"ruleId": "488", "severity": 1, "message": "551", "line": 14, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 14, "endColumn": 11}, {"ruleId": "488", "severity": 1, "message": "552", "line": 18, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 18, "endColumn": 15}, {"ruleId": "488", "severity": 1, "message": "553", "line": 19, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 19, "endColumn": 19}, {"ruleId": "488", "severity": 1, "message": "554", "line": 23, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 23, "endColumn": 15}, {"ruleId": "488", "severity": 1, "message": "555", "line": 26, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 26, "endColumn": 16}, {"ruleId": "488", "severity": 1, "message": "556", "line": 41, "column": 9, "nodeType": "490", "messageId": "491", "endLine": 41, "endColumn": 14}, {"ruleId": "488", "severity": 1, "message": "504", "line": 41, "column": 16, "nodeType": "490", "messageId": "491", "endLine": 41, "endColumn": 20}, {"ruleId": "488", "severity": 1, "message": "505", "line": 41, "column": 22, "nodeType": "490", "messageId": "491", "endLine": 41, "endColumn": 31}, {"ruleId": "488", "severity": 1, "message": "557", "line": 22, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 22, "endColumn": 18}, {"ruleId": "488", "severity": 1, "message": "558", "line": 10, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 10, "endColumn": 19}, {"ruleId": "488", "severity": 1, "message": "557", "line": 11, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 11, "endColumn": 18}, {"ruleId": "488", "severity": 1, "message": "559", "line": 8, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 8, "endColumn": 8}, {"ruleId": "488", "severity": 1, "message": "560", "line": 20, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 20, "endColumn": 17}, {"ruleId": "488", "severity": 1, "message": "561", "line": 21, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 21, "endColumn": 17}, {"ruleId": "488", "severity": 1, "message": "562", "line": 23, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 23, "endColumn": 15}, {"ruleId": "488", "severity": 1, "message": "563", "line": 63, "column": 7, "nodeType": "490", "messageId": "491", "endLine": 63, "endColumn": 16}, {"ruleId": "564", "severity": 1, "message": "565", "line": 320, "column": 17, "nodeType": "566", "endLine": 320, "endColumn": 31}, {"ruleId": "488", "severity": 1, "message": "567", "line": 25, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 25, "endColumn": 17}, {"ruleId": "522", "severity": 1, "message": "568", "line": 64, "column": 6, "nodeType": "524", "endLine": 64, "endColumn": 8, "suggestions": "569"}, {"ruleId": "488", "severity": 1, "message": "567", "line": 19, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 19, "endColumn": 17}, {"ruleId": "488", "severity": 1, "message": "531", "line": 23, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 23, "endColumn": 16}, {"ruleId": "522", "severity": 1, "message": "523", "line": 50, "column": 6, "nodeType": "524", "endLine": 50, "endColumn": 8, "suggestions": "570"}, {"ruleId": "488", "severity": 1, "message": "571", "line": 22, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 22, "endColumn": 15}, {"ruleId": "522", "severity": 1, "message": "572", "line": 47, "column": 6, "nodeType": "524", "endLine": 47, "endColumn": 8, "suggestions": "573"}, {"ruleId": "488", "severity": 1, "message": "567", "line": 18, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 18, "endColumn": 17}, {"ruleId": "488", "severity": 1, "message": "574", "line": 21, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 21, "endColumn": 17}, {"ruleId": "488", "severity": 1, "message": "507", "line": 8, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 8, "endColumn": 10}, {"ruleId": "488", "severity": 1, "message": "498", "line": 13, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 13, "endColumn": 8}, {"ruleId": "488", "severity": 1, "message": "526", "line": 15, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 15, "endColumn": 7}, {"ruleId": "488", "severity": 1, "message": "575", "line": 17, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 17, "endColumn": 8}, {"ruleId": "488", "severity": 1, "message": "576", "line": 377, "column": 15, "nodeType": "490", "messageId": "491", "endLine": 377, "endColumn": 21}, {"ruleId": "488", "severity": 1, "message": "507", "line": 8, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 8, "endColumn": 10}, {"ruleId": "488", "severity": 1, "message": "536", "line": 10, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 10, "endColumn": 6}, {"ruleId": "488", "severity": 1, "message": "577", "line": 34, "column": 8, "nodeType": "490", "messageId": "491", "endLine": 34, "endColumn": 15}, {"ruleId": "488", "severity": 1, "message": "495", "line": 35, "column": 10, "nodeType": "490", "messageId": "491", "endLine": 35, "endColumn": 15}, {"ruleId": "488", "severity": 1, "message": "505", "line": 38, "column": 15, "nodeType": "490", "messageId": "491", "endLine": 38, "endColumn": 24}, {"ruleId": "488", "severity": 1, "message": "518", "line": 333, "column": 28, "nodeType": "490", "messageId": "491", "endLine": 333, "endColumn": 32}, {"ruleId": "522", "severity": 1, "message": "578", "line": 346, "column": 6, "nodeType": "524", "endLine": 346, "endColumn": 14, "suggestions": "579"}, {"ruleId": "488", "severity": 1, "message": "580", "line": 1, "column": 17, "nodeType": "490", "messageId": "491", "endLine": 1, "endColumn": 25}, {"ruleId": "488", "severity": 1, "message": "506", "line": 3, "column": 15, "nodeType": "490", "messageId": "491", "endLine": 3, "endColumn": 20}, {"ruleId": "488", "severity": 1, "message": "550", "line": 3, "column": 52, "nodeType": "490", "messageId": "491", "endLine": 3, "endColumn": 56}, {"ruleId": "488", "severity": 1, "message": "581", "line": 10, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 10, "endColumn": 15}, {"ruleId": "488", "severity": 1, "message": "555", "line": 12, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 12, "endColumn": 16}, {"ruleId": "488", "severity": 1, "message": "582", "line": 13, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 13, "endColumn": 19}, {"ruleId": "488", "severity": 1, "message": "495", "line": 23, "column": 10, "nodeType": "490", "messageId": "491", "endLine": 23, "endColumn": 15}, {"ruleId": "488", "severity": 1, "message": "538", "line": 5, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 5, "endColumn": 11}, {"ruleId": "488", "severity": 1, "message": "512", "line": 6, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 6, "endColumn": 8}, {"ruleId": "488", "severity": 1, "message": "536", "line": 10, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 10, "endColumn": 6}, {"ruleId": "488", "severity": 1, "message": "507", "line": 12, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 12, "endColumn": 10}, {"ruleId": "488", "severity": 1, "message": "527", "line": 20, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 20, "endColumn": 19}, {"ruleId": "488", "severity": 1, "message": "542", "line": 26, "column": 8, "nodeType": "490", "messageId": "491", "endLine": 26, "endColumn": 14}, {"ruleId": "488", "severity": 1, "message": "583", "line": 27, "column": 8, "nodeType": "490", "messageId": "491", "endLine": 27, "endColumn": 12}, {"ruleId": "488", "severity": 1, "message": "504", "line": 31, "column": 9, "nodeType": "490", "messageId": "491", "endLine": 31, "endColumn": 13}, {"ruleId": "488", "severity": 1, "message": "584", "line": 294, "column": 10, "nodeType": "490", "messageId": "491", "endLine": 294, "endColumn": 20}, {"ruleId": "488", "severity": 1, "message": "585", "line": 294, "column": 22, "nodeType": "490", "messageId": "491", "endLine": 294, "endColumn": 35}, {"ruleId": "488", "severity": 1, "message": "506", "line": 6, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 6, "endColumn": 8}, {"ruleId": "488", "severity": 1, "message": "508", "line": 11, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 11, "endColumn": 6}, {"ruleId": "488", "severity": 1, "message": "509", "line": 12, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 12, "endColumn": 6}, {"ruleId": "488", "severity": 1, "message": "575", "line": 13, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 13, "endColumn": 8}, {"ruleId": "488", "severity": 1, "message": "519", "line": 18, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 18, "endColumn": 8}, {"ruleId": "488", "severity": 1, "message": "495", "line": 36, "column": 10, "nodeType": "490", "messageId": "491", "endLine": 36, "endColumn": 15}, {"ruleId": "488", "severity": 1, "message": "586", "line": 355, "column": 57, "nodeType": "490", "messageId": "491", "endLine": 355, "endColumn": 65}, {"ruleId": "488", "severity": 1, "message": "519", "line": 2, "column": 10, "nodeType": "490", "messageId": "491", "endLine": 2, "endColumn": 15}, {"ruleId": "488", "severity": 1, "message": "504", "line": 17, "column": 9, "nodeType": "490", "messageId": "491", "endLine": 17, "endColumn": 13}, {"ruleId": "522", "severity": 1, "message": "587", "line": 38, "column": 6, "nodeType": "524", "endLine": 38, "endColumn": 31, "suggestions": "588"}, {"ruleId": "522", "severity": 1, "message": "589", "line": 153, "column": 6, "nodeType": "524", "endLine": 153, "endColumn": 16, "suggestions": "590"}, {"ruleId": "488", "severity": 1, "message": "559", "line": 2, "column": 47, "nodeType": "490", "messageId": "491", "endLine": 2, "endColumn": 52}, {"ruleId": "488", "severity": 1, "message": "536", "line": 2, "column": 54, "nodeType": "490", "messageId": "491", "endLine": 2, "endColumn": 57}, {"ruleId": "488", "severity": 1, "message": "591", "line": 8, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 8, "endColumn": 18}, {"ruleId": "488", "severity": 1, "message": "592", "line": 9, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 9, "endColumn": 20}, {"ruleId": "488", "severity": 1, "message": "593", "line": 12, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 12, "endColumn": 22}, {"ruleId": "488", "severity": 1, "message": "594", "line": 15, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 15, "endColumn": 22}, {"ruleId": "488", "severity": 1, "message": "595", "line": 3, "column": 81, "nodeType": "490", "messageId": "491", "endLine": 3, "endColumn": 87}, {"ruleId": "488", "severity": 1, "message": "596", "line": 7, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 7, "endColumn": 15}, {"ruleId": "488", "severity": 1, "message": "553", "line": 8, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 8, "endColumn": 19}, {"ruleId": "488", "severity": 1, "message": "597", "line": 9, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 9, "endColumn": 15}, {"ruleId": "488", "severity": 1, "message": "558", "line": 10, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 10, "endColumn": 19}, {"ruleId": "488", "severity": 1, "message": "598", "line": 19, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 19, "endColumn": 28}, {"ruleId": "488", "severity": 1, "message": "502", "line": 21, "column": 3, "nodeType": "490", "messageId": "491", "endLine": 21, "endColumn": 22}, {"ruleId": "488", "severity": 1, "message": "556", "line": 30, "column": 9, "nodeType": "490", "messageId": "491", "endLine": 30, "endColumn": 14}, {"ruleId": "488", "severity": 1, "message": "504", "line": 30, "column": 16, "nodeType": "490", "messageId": "491", "endLine": 30, "endColumn": 20}, {"ruleId": "488", "severity": 1, "message": "511", "line": 2, "column": 38, "nodeType": "490", "messageId": "491", "endLine": 2, "endColumn": 43}, {"ruleId": "488", "severity": 1, "message": "599", "line": 14, "column": 36, "nodeType": "490", "messageId": "491", "endLine": 14, "endColumn": 47}, {"ruleId": "488", "severity": 1, "message": "517", "line": 17, "column": 9, "nodeType": "490", "messageId": "491", "endLine": 17, "endColumn": 15}, {"ruleId": "488", "severity": 1, "message": "600", "line": 5, "column": 10, "nodeType": "490", "messageId": "491", "endLine": 5, "endColumn": 25}, {"ruleId": "522", "severity": 1, "message": "601", "line": 149, "column": 6, "nodeType": "524", "endLine": 149, "endColumn": 8, "suggestions": "602"}, {"ruleId": "522", "severity": 1, "message": "603", "line": 150, "column": 39, "nodeType": "490", "endLine": 150, "endColumn": 46}, {"ruleId": "604", "severity": 1, "message": "605", "line": 383, "column": 1, "nodeType": "606", "endLine": 405, "endColumn": 3}, {"ruleId": "488", "severity": 1, "message": "556", "line": 18, "column": 9, "nodeType": "490", "messageId": "491", "endLine": 18, "endColumn": 14}, {"ruleId": "488", "severity": 1, "message": "504", "line": 18, "column": 16, "nodeType": "490", "messageId": "491", "endLine": 18, "endColumn": 20}, {"ruleId": "488", "severity": 1, "message": "583", "line": 2, "column": 18, "nodeType": "490", "messageId": "491", "endLine": 2, "endColumn": 22}, {"ruleId": "488", "severity": 1, "message": "506", "line": 2, "column": 24, "nodeType": "490", "messageId": "491", "endLine": 2, "endColumn": 29}, {"ruleId": "488", "severity": 1, "message": "556", "line": 8, "column": 9, "nodeType": "490", "messageId": "491", "endLine": 8, "endColumn": 14}, "@typescript-eslint/no-unused-vars", "'ReloadOutlined' is defined but never used.", "Identifier", "unusedVar", "'HomeOutlined' is defined but never used.", "'ExclamationCircleOutlined' is defined but never used.", "'styled' is defined but never used.", "'theme' is defined but never used.", "'Statistic' is defined but never used.", "'Badge' is defined but never used.", "'Modal' is defined but never used.", "'StarFilled' is defined but never used.", "'EyeOutlined' is defined but never used.", "'SafetyOutlined' is defined but never used.", "'ThunderboltOutlined' is defined but never used.", "'CrownOutlined' is defined but never used.", "'Text' is assigned a value but never used.", "'Paragraph' is assigned a value but never used.", "'Space' is defined but never used.", "'Divider' is defined but never used.", "'Row' is defined but never used.", "'Col' is defined but never used.", "'Form' is defined but never used.", "'Input' is defined but never used.", "'Radio' is defined but never used.", "'List' is defined but never used.", "'Image' is defined but never used.", "'CreditCardOutlined' is defined but never used.", "'CheckCircleOutlined' is defined but never used.", "'Option' is assigned a value but never used.", "'user' is assigned a value but never used.", "'Empty' is defined but never used.", "'MessageOutlined' is defined but never used.", "'Step' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadOrders'. Either include it or remove the dependency array.", "ArrayExpression", ["607"], "'Spin' is defined but never used.", "'CalendarOutlined' is defined but never used.", "'LikeOutlined' is defined but never used.", "'DislikeOutlined' is defined but never used.", "'MoreOutlined' is defined but never used.", "'TruckOutlined' is defined but never used.", "'activeImageIndex' is assigned a value but never used.", "'setActiveImageIndex' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'loadBookDetail' and 'loadRecommendedBooks'. Either include them or remove the dependency array.", ["608"], "'Tag' is defined but never used.", "'Slider' is defined but never used.", "'Checkbox' is defined but never used.", "'FilterOutlined' is defined but never used.", "'ClearOutlined' is defined but never used.", "'AdvancedSearch' is defined but never used.", "'Button' is defined but never used.", "'Panel' is assigned a value but never used.", "'setSearchParams' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadBooks'. Either include it or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["609"], "'handleSearch' is assigned a value but never used.", "'conditionOptions' is assigned a value but never used.", "'Avatar' is defined but never used.", "'Rate' is defined but never used.", "'Skeleton' is defined but never used.", "'UserOutlined' is defined but never used.", "'ShoppingOutlined' is defined but never used.", "'GiftOutlined' is defined but never used.", "'HeartOutlined' is defined but never used.", "'Title' is assigned a value but never used.", "'SettingOutlined' is defined but never used.", "'BarChartOutlined' is defined but never used.", "'Table' is defined but never used.", "'DollarOutlined' is defined but never used.", "'TrophyOutlined' is defined but never used.", "'FallOutlined' is defined but never used.", "'ChartCard' is assigned a value but never used.", "jsx-a11y/anchor-is-valid", "The href attribute is required for an anchor to be keyboard accessible. Provide a valid, navigable address as the href value. If you cannot provide an href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'SearchOutlined' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadBooks'. Either include it or remove the dependency array.", ["610"], ["611"], "'DragOutlined' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadCategories'. Either include it or remove the dependency array.", ["612"], "'DeleteOutlined' is defined but never used.", "'Alert' is defined but never used.", "'values' is assigned a value but never used.", "'Loading' is defined but never used.", "React Hook useEffect has missing dependencies: 'loadReviews' and 'loadStats'. Either include them or remove the dependency array.", ["613"], "'useState' is defined but never used.", "'StarOutlined' is defined but never used.", "'ShareAltOutlined' is defined but never used.", "'Card' is defined but never used.", "'priceRange' is assigned a value but never used.", "'setPriceRange' is assigned a value but never used.", "'getTotal' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'checkFavoriteStatus'. Either include it or remove the dependency array.", ["614"], "React Hook useEffect has missing dependencies: 'handleImageError' and 'handleImageLoad'. Either include them or remove the dependency array.", ["615"], "'ArrowUpOutlined' is defined but never used.", "'ArrowDownOutlined' is defined but never used.", "'ClockCircleOutlined' is defined but never used.", "'CloudServerOutlined' is defined but never used.", "'Switch' is defined but never used.", "'BookOutlined' is defined but never used.", "'TagsOutlined' is defined but never used.", "'SafetyCertificateOutlined' is defined but never used.", "'CustomModal' is defined but never used.", "'pageTransitions' is defined but never used.", "React Hook useEffect has missing dependencies: 'duration' and 'type'. Either include them or remove the dependency array.", ["616"], "The ref value 'elementRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'elementRef.current' to a variable inside the effect, and use that variable in the cleanup function.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", {"desc": "617", "fix": "618"}, {"desc": "619", "fix": "620"}, {"desc": "621", "fix": "622"}, {"desc": "623", "fix": "624"}, {"desc": "625", "fix": "626"}, {"desc": "627", "fix": "628"}, {"desc": "629", "fix": "630"}, {"desc": "631", "fix": "632"}, {"desc": "633", "fix": "634"}, {"desc": "635", "fix": "636"}, "Update the dependencies array to be: [activeTab, loadOrders]", {"range": "637", "text": "638"}, "Update the dependencies array to be: [id, loadBookDetail, loadRecommendedBooks]", {"range": "639", "text": "640"}, "Update the dependencies array to be: [filters, loadBooks]", {"range": "641", "text": "642"}, "Update the dependencies array to be: [loadBooks]", {"range": "643", "text": "644"}, "Update the dependencies array to be: [loadOrders]", {"range": "645", "text": "646"}, "Update the dependencies array to be: [loadCategories]", {"range": "647", "text": "648"}, "Update the dependencies array to be: [bookId, loadReviews, loadStats]", {"range": "649", "text": "650"}, "Update the dependencies array to be: [isAuthenticated, bookId, checkFavoriteStatus]", {"range": "651", "text": "652"}, "Update the dependencies array to be: [handleImageError, handleImageLoad, imageSrc]", {"range": "653", "text": "654"}, "Update the dependencies array to be: [duration, type]", {"range": "655", "text": "656"}, [8505, 8516], "[activeTab, loadOrders]", [17885, 17889], "[id, loadBookDetail, loadRecommendedBooks]", [16923, 16952], "[filters, loadBooks]", [1447, 1449], "[loadBooks]", [1154, 1156], "[loadOrders]", [1140, 1142], "[loadCategories]", [7050, 7058], "[bookId, loadReviews, loadStats]", [1078, 1103], "[isAuthenticated, bookId, checkFavoriteStatus]", [3301, 3311], "[handleImageError, handleImageLoad, imageSrc]", [3929, 3931], "[duration, type]"]